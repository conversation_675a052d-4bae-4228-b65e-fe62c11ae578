<template>
  <div class="message-content">
    <!-- 数据类型选择器 -->
    <div class="message-type-selector">
      <el-select v-model="messageType" size="small" @change="handleMessageTypeChange" class="type-selector">
        <el-option value="text" :label="t('文本')">
          <span class="option-content">
            <span>{{ t("文本") }}</span>
          </span>
        </el-option>
        <el-option value="json" :label="t('JSON')">
          <span class="option-content">
            <span>JSON</span>
          </span>
        </el-option>
        <el-option value="xml" :label="t('XML')">
          <span class="option-content">
            <span>XML</span>
          </span>
        </el-option>
        <el-option value="html" :label="t('HTML')">
          <span class="option-content">
            <span>HTML</span>
          </span>
        </el-option>
        <el-option value="binary-base64" :label="t('二进制(Base64)')">
          <span class="option-content">
            <span>{{ t("二进制(Base64)") }}</span>
          </span>
        </el-option>
        <el-option value="binary-hex" :label="t('二进制(Hex)')">
          <span class="option-content">
            <span>{{ t("二进制(Hex)") }}</span>
          </span>
        </el-option>
      </el-select>
    </div>

    <!-- 内容编辑器 -->
    <div class="content-editor">
      <SJsonEditor
          v-model="messageContent"
          :config="editorConfig"
          :auto-height="false"
        />
    </div>

    <!-- 操作按钮区域 -->
    <div class="content-actions">
      <div class="action-buttons">
        <el-tooltip
          :content="props.connectionState !== 'connected' ? t('等待连接') : ''"
          :disabled="props.connectionState === 'connected'"
          placement="top"
        >
          <el-button
            type="primary"
            :disabled="!messageContent.trim() || props.connectionState !== 'connected'"
            @click="handleSendMessage"
            :icon="Position"
          >
            {{ t("发送消息") }}
          </el-button>
        </el-tooltip>

        <el-checkbox
          v-model="sendAndClear"
          @change="handleSendAndClearChange"
        >
          {{ t("发送并清空") }}
        </el-checkbox>

        <!-- 自动心跳功能 -->
        <div class="heartbeat-controls">
          <el-checkbox
            v-model="autoHeartbeat"
            @change="handleAutoHeartbeatChange"
            :disabled="props.connectionState !== 'connected'"
          >
            {{ t("自动发送心跳包") }}
          </el-checkbox>

          <div v-if="autoHeartbeat" class="heartbeat-config">
            <el-input-number
              v-model="heartbeatInterval"
              :min="1000"
              :max="300000"
              :step="1000"
              size="small"
              @change="handleHeartbeatIntervalChange"
              style="width: 120px;"
            />
            <span class="interval-unit">{{ t("毫秒") }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useTranslation } from 'i18next-vue'
import { useRoute } from 'vue-router'
import { useApidocTas } from '@/store/apidoc/tabs'
import { useWebSocket } from '@/store/websocket/websocket'
import { webSocketNodeCache } from '@/cache/websocketNode'
import { ElMessage } from 'element-plus'
import { debounce } from '@/helper'
import {
  Position,
} from '@element-plus/icons-vue'
import SJsonEditor from '@/components/common/json-editor/g-json-editor.vue'

const props = withDefaults(defineProps<{
  connectionState?: string
  connectionId?: string
}>(), {
  connectionState: 'disconnected',
  connectionId: ''
})

const { t } = useTranslation()
const route = useRoute()
const apidocTabsStore = useApidocTas()
const websocketStore = useWebSocket()

// 消息类型定义
type MessageType = 'text' | 'json' | 'xml' | 'html' | 'binary-base64' | 'binary-hex'

// 获取当前选中的tab
const currentSelectTab = computed(() => {
  const projectId = route.query.id as string
  const tabs = apidocTabsStore.tabs[projectId]
  const currentSelectTab = tabs?.find((tab) => tab.selected) || null
  return currentSelectTab
})

// 响应式数据
const messageContent = ref('')
const sendAndClear = ref(false)
const messageType = ref<MessageType>('text')
const autoHeartbeat = ref(false)
const heartbeatInterval = ref(30000) // 30 seconds default
let heartbeatTimer: NodeJS.Timeout | null = null

const editorConfig = computed(() => {
  switch (messageType.value) {
    case 'json':
      return { language: 'json' }
    case 'xml':
      return { language: 'xml' }
    case 'html':
      return { language: 'html' }
    case 'text':
    default:
      return { language: 'plaintext' }
  }
})


// 初始化状态
const initStates = () => {
  if (currentSelectTab.value) {
    sendAndClear.value = webSocketNodeCache.getWebSocketSendAndClearState(currentSelectTab.value._id)
    messageType.value = webSocketNodeCache.getWebSocketMessageType(currentSelectTab.value._id) as MessageType

    // 从WebSocket store加载消息内容和心跳设置
    messageContent.value = websocketStore.websocket.item.sendMessage || ''
    autoHeartbeat.value = websocketStore.websocket.item.autoHeartbeat || false
    heartbeatInterval.value = websocketStore.websocket.item.heartbeatInterval || 30000
  }
}

// 方法
const handleSendMessage = async () => {
  if (!messageContent.value.trim()) {
    ElMessage.warning(t('消息内容不能为空'))
    return
  }

  if (!props.connectionId) {
    ElMessage.error(t('WebSocket连接不存在'))
    return
  }

  if (props.connectionState !== 'connected') {
    ElMessage.error(t('WebSocket未连接'))
    return
  }

  try {
    const result = await window.electronAPI?.websocket.send(props.connectionId, messageContent.value)
    if (result?.success) {
      if (sendAndClear.value) {
        messageContent.value = ''
      }
    } else {
      ElMessage.error(t('消息发送失败') + ': ' + (result?.error || t('未知错误')))
      console.error('WebSocket消息发送失败:', result?.error)
    }
  } catch (error) {
    ElMessage.error(t('消息发送异常'))
    console.error('WebSocket消息发送异常:', error)
  }
}

const handleSendAndClearChange = (value: boolean | string | number) => {
  const boolValue = Boolean(value)
  if (currentSelectTab.value) {
    webSocketNodeCache.setWebSocketSendAndClearState(currentSelectTab.value._id, boolValue)
  }
}

const handleMessageTypeChange = (value: MessageType) => {
  messageType.value = value
  if (currentSelectTab.value) {
    webSocketNodeCache.setWebSocketMessageType(currentSelectTab.value._id, value)
  }
}

// 心跳相关方法
const handleAutoHeartbeatChange = (enabled: boolean | string | number) => {
  const boolEnabled = Boolean(enabled)
  autoHeartbeat.value = boolEnabled
  websocketStore.changeWebSocketAutoHeartbeat(boolEnabled)

  if (boolEnabled && props.connectionState === 'connected') {
    startHeartbeat()
    ElMessage.success(t('自动心跳已启用'))
  } else {
    stopHeartbeat()
    if (!boolEnabled) {
      ElMessage.info(t('自动心跳已停止'))
    }
  }
}

const handleHeartbeatIntervalChange = (interval: number | undefined) => {
  if (interval !== undefined) {
    heartbeatInterval.value = interval
    websocketStore.changeWebSocketHeartbeatInterval(interval)

    // 如果心跳正在运行，重新启动以应用新的间隔
    if (autoHeartbeat.value && props.connectionState === 'connected') {
      stopHeartbeat()
      startHeartbeat()
    }
  }
}

const startHeartbeat = () => {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer)
  }

  heartbeatTimer = setInterval(async () => {
    if (props.connectionState === 'connected' && props.connectionId) {
      try {
        const heartbeatContent = messageContent.value.trim() || '{"type": "ping", "timestamp": ' + Date.now() + '}'
        const result = await window.electronAPI?.websocket.send(props.connectionId, heartbeatContent)
        if (!result?.success) {
          console.error('心跳包发送失败:', result?.error)
        }
      } catch (error) {
        console.error('心跳包发送异常:', error)
      }
    }
  }, heartbeatInterval.value)
}

const stopHeartbeat = () => {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer)
    heartbeatTimer = null
  }
}

const handleFormatJson = () => {
  if (messageType.value === 'json' && messageContent.value.trim()) {
    try {
      const parsed = JSON.parse(messageContent.value)
      messageContent.value = JSON.stringify(parsed, null, 2)
      ElMessage.success(t('JSON格式化成功'))
    } catch (error) {
      ElMessage.error(t('JSON格式无效，无法格式化'))
    }
  }
}

// 创建防抖的保存函数
const debouncedSaveMessage = debounce((content: string) => {
  websocketStore.changeWebSocketSendMessage(content)
}, 300)

// 监听消息内容变化，自动保存到store
watch(messageContent, (newContent) => {
  debouncedSaveMessage(newContent)
})

// 监听连接状态变化，管理心跳
watch(() => props.connectionState, (newState) => {
  if (newState === 'connected' && autoHeartbeat.value) {
    startHeartbeat()
  } else if (newState !== 'connected') {
    stopHeartbeat()
  }
})

// 监听当前选中tab变化，重新加载状态
watch(currentSelectTab, (newTab) => {
  if (newTab) {
    // 停止之前的心跳
    stopHeartbeat()

    // 重新加载状态
    sendAndClear.value = webSocketNodeCache.getWebSocketSendAndClearState(newTab._id)
    messageType.value = webSocketNodeCache.getWebSocketMessageType(newTab._id) as MessageType

    // 从WebSocket store加载消息内容和心跳设置
    messageContent.value = websocketStore.websocket.item.sendMessage || ''
    autoHeartbeat.value = websocketStore.websocket.item.autoHeartbeat || false
    heartbeatInterval.value = websocketStore.websocket.item.heartbeatInterval || 30000

    // 如果连接状态是已连接且启用了自动心跳，启动心跳
    if (props.connectionState === 'connected' && autoHeartbeat.value) {
      startHeartbeat()
    }
  }
})

onMounted(() => {
  initStates()
})

onUnmounted(() => {
  stopHeartbeat()
})
</script>

<style lang="scss" scoped>
.message-content {
  padding: 0 16px;
  height: 100%;
  .message-type-selector {
    margin-bottom: 5px;
    .type-selector {
      width: 120px;
    }
  }

  .content-editor {
    height: calc(100vh - 300px);
    border: 1px solid var(--gray-400);
    .binary-editor {
      flex: 1;
      .binary-input {
        height: 100%;

        :deep(.el-textarea__inner) {
          height: 100% !important;
          resize: none;
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .content-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    border-top: 1px solid var(--el-border-color-lighter);

    .action-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      flex-wrap: wrap;
    }

    .action-options {
      display: flex;
      align-items: center;
    }

    .heartbeat-controls {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-left: 16px;

      .heartbeat-config {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: 24px;

        .interval-unit {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}
</style>
